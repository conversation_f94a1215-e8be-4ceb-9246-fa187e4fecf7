# Changes

## 1.0.1

### Other changes

* project: migrate to gitlab (0046018)

## 1.0.0

* Update:a83cc21 Update dependencies. (a83cc21)
* Fix:0c80670 Fix component info. (0c80670)
* chore: add change log (2787c9f)
* breaking change: export the entrypoint function directly (e56e066)
* chore: set indentation to two spaces (04419ce)
* chore: convert the unit tests to vanilla js (d87f514)
* feature: add browser-based test runner (24bdeab)
* chore: update dev deps (d56cbd4)
* fix: fix lint errors (52ae898)
* chore: update target node versions (d5a8b6b)
* fix: speed up the unit tests a bit (b8adf00)
* chore: insert whitespace (012e12b)
* breaking change: rename from trier to tryer (d7b121d)
* fix: tolerate missing options argument (7417ed9)
* fix: improve the readme (807112e)
* breaking change: ditch the stupid context and args options (009ef33)
* feature: handle promises returned by actions (e24a0cf)
* fix: fix lint errors (d4e6321)
* chore: update minified lib (066bbc5)
* chore: add a note about lib size to the readme (55b883b)
* chore: update copyright statement (3805ac6)

## 0.3.6

Legacy version, `trier`.

