{"name": "sanitize.css", "version": "13.0.0", "description": "A best-practices CSS foundation", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>> (http://jonathantneal.com/)", "<PERSON> <<EMAIL>> (http://nicolasgallagher.com/)"], "license": "CC0-1.0", "repository": "csstools/sanitize.css", "homepage": "https://github.com/csstools/sanitize.css#readme", "bugs": "https://github.com/csstools/sanitize.css/issues", "main": "sanitize.css", "style": "sanitize.css", "files": ["assets.css", "forms.css", "reduce-motion.css", "sanitize.css", "system-ui.css", "typography.css", "ui-monospace.css"], "scripts": {"prepublishOnly": "npm test", "test": "stylelint *.css"}, "devDependencies": {"stylelint": "^13.13.1", "stylelint-config-standard": "^22.0.0"}, "stylelint": {"extends": "stylelint-config-standard", "rules": {"font-family-no-duplicate-names": [true, {"ignoreFontFamilyNames": ["monospace"]}], "no-descending-specificity": [null]}}, "keywords": ["css", "normalizes", "sanitizes", "browsers", "fixes"]}