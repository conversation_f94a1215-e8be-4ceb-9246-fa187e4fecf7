# cssnano-utils

Utility methods and plugin for cssnano projects

## List of methods and plugin(s)

| **utility methods** | **description**                                                           |
| ------------------- | ------------------------------------------------------------------------- |
| `rawCache`          | Postcss plugin to manage the raw value formatting for generated AST nodes |
| `getArguments`      | Get a list of arguments, separated by a comma.                            |
| `sameParent`        | Check that two PostCSS nodes share the same parent.                       |

## Contributors

See [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).
