export const shorthandData = {
  'animation': [
    'animation-name',
    'animation-duration',
    'animation-timing-function',
    'animation-delay',
    'animation-iteration-count',
    'animation-direction',
    'animation-fill-mode',
    'animation-play-state',
  ],
  'background': [
    'background-image',
    'background-size',
    'background-position',
    'background-repeat',
    'background-origin',
    'background-clip',
    'background-attachment',
    'background-color',
  ],
  'columns': [
    'column-width',
    'column-count',
  ],
  'column-rule': [
    'column-rule-width',
    'column-rule-style',
    'column-rule-color',
  ],
  'flex': [
    'flex-grow',
    'flex-shrink',
    'flex-basis',
  ],
  'flex-flow': [
    'flex-direction',
    'flex-wrap',
  ],
  'font': [
    'font-style',
    'font-variant',
    'font-weight',
    'font-stretch',
    'font-size',
    'font-family',
    'line-height',
  ],
  'grid': [
    'grid-template-rows',
    'grid-template-columns',
    'grid-template-areas',
    'grid-auto-rows',
    'grid-auto-columns',
    'grid-auto-flow',
    'column-gap',
    'row-gap',
  ],
  'grid-area': [
    'grid-row-start',
    'grid-column-start',
    'grid-row-end',
    'grid-column-end',
  ],
  'grid-column': [
    'grid-column-start',
    'grid-column-end',
  ],
  'grid-row': [
    'grid-row-start',
    'grid-row-end',
  ],
  'grid-template': [
    'grid-template-columns',
    'grid-template-rows',
    'grid-template-areas',
  ],
  'list-style': [
    'list-style-type',
    'list-style-position',
    'list-style-image',
  ],
  'padding': [
    'padding-block',
    'padding-block-start',
    'padding-block-end',
    'padding-inline',
    'padding-inline-start',
    'padding-inline-end',
    'padding-top',
    'padding-right',
    'padding-bottom',
    'padding-left',
  ],
  'padding-block': [
    'padding-block-start',
    'padding-block-end',
    'padding-top',
    'padding-right',
    'padding-bottom',
    'padding-left',
  ],
  'padding-block-start': [
    'padding-top',
    'padding-right',
    'padding-left',
  ],
  'padding-block-end': [
    'padding-right',
    'padding-bottom',
    'padding-left',
  ],
  'padding-inline': [
    'padding-inline-start',
    'padding-inline-end',
    'padding-top',
    'padding-right',
    'padding-bottom',
    'padding-left',
  ],
  'padding-inline-start': [
    'padding-top',
    'padding-right',
    'padding-left',
  ],
  'padding-inline-end': [
    'padding-right',
    'padding-bottom',
    'padding-left',
  ],
  'margin': [
    'margin-block',
    'margin-block-start',
    'margin-block-end',
    'margin-inline',
    'margin-inline-start',
    'margin-inline-end',
    'margin-top',
    'margin-right',
    'margin-bottom',
    'margin-left',
  ],
  'margin-block': [
    'margin-block-start',
    'margin-block-end',
    'margin-top',
    'margin-right',
    'margin-bottom',
    'margin-left',
  ],
  'margin-inline': [
    'margin-inline-start',
    'margin-inline-end',
    'margin-top',
    'margin-right',
    'margin-bottom',
    'margin-left',
  ],
  'margin-inline-start': [
    'margin-top',
    'margin-right',
    'margin-bottom',
    'margin-left',
  ],
  'margin-inline-end': [
    'margin-top',
    'margin-right',
    'margin-bottom',
    'margin-left',
  ],
  'border': [
    'border-top',
    'border-right',
    'border-bottom',
    'border-left',
    'border-width',
    'border-style',
    'border-color',
    'border-top-width',
    'border-right-width',
    'border-bottom-width',
    'border-left-width',
    'border-inline-start-width',
    'border-inline-end-width',
    'border-block-start-width',
    'border-block-end-width',
    'border-top-style',
    'border-right-style',
    'border-bottom-style',
    'border-left-style',
    'border-inline-start-style',
    'border-inline-end-style',
    'border-block-start-style',
    'border-block-end-style',
    'border-top-color',
    'border-right-color',
    'border-bottom-color',
    'border-left-color',
    'border-inline-start-color',
    'border-inline-end-color',
    'border-block-start-color',
    'border-block-end-color',
    'border-block',
    'border-block-start',
    'border-block-end',
    'border-block-width',
    'border-block-style',
    'border-block-color',
    'border-inline',
    'border-inline-start',
    'border-inline-end',
    'border-inline-width',
    'border-inline-style',
    'border-inline-color',
  ],
  'border-top': [
    'border-width',
    'border-style',
    'border-color',
    'border-top-width',
    'border-top-style',
    'border-top-color',
  ],
  'border-right': [
    'border-width',
    'border-style',
    'border-color',
    'border-right-width',
    'border-right-style',
    'border-right-color',
  ],
  'border-bottom': [
    'border-width',
    'border-style',
    'border-color',
    'border-bottom-width',
    'border-bottom-style',
    'border-bottom-color',
  ],
  'border-left': [
    'border-width',
    'border-style',
    'border-color',
    'border-left-width',
    'border-left-style',
    'border-left-color',
  ],
  'border-color': [
    'border-top-color',
    'border-bottom-color',
    'border-left-color',
    'border-right-color',
    'border-inline-start-color',
    'border-inline-end-color',
    'border-block-start-color',
    'border-block-end-color',
  ],
  'border-width': [
    'border-top-width',
    'border-bottom-width',
    'border-left-width',
    'border-right-width',
    'border-inline-start-width',
    'border-inline-end-width',
    'border-block-start-width',
    'border-block-end-width',
  ],
  'border-style': [
    'border-top-style',
    'border-bottom-style',
    'border-left-style',
    'border-right-style',
    'border-inline-start-style',
    'border-inline-end-style',
    'border-block-start-style',
    'border-block-end-style',
  ],
  'border-radius': [
    'border-top-right-radius',
    'border-top-left-radius',
    'border-bottom-right-radius',
    'border-bottom-left-radius',
  ],
  'border-block': [
    'border-block-start',
    'border-block-end',
    'border-block-width',
    'border-width',
    'border-block-style',
    'border-style',
    'border-block-color',
    'border-color',
  ],
  'border-block-start': [
    'border-block-start-width',
    'border-width',
    'border-block-start-style',
    'border-style',
    'border-block-start-color',
    'border-color',
  ],
  'border-block-end': [
    'border-block-end-width',
    'border-width',
    'border-block-end-style',
    'border-style',
    'border-block-end-color',
    'border-color',
  ],
  'border-inline': [
    'border-inline-start',
    'border-inline-end',
    'border-inline-width',
    'border-width',
    'border-inline-style',
    'border-style',
    'border-inline-color',
    'border-color',
  ],
  'border-inline-start': [
    'border-inline-start-width',
    'border-width',
    'border-inline-start-style',
    'border-style',
    'border-inline-start-color',
    'border-color',
  ],
  'border-inline-end': [
    'border-inline-end-width',
    'border-width',
    'border-inline-end-style',
    'border-style',
    'border-inline-end-color',
    'border-color',
  ],
  'border-image': [
    'border-image-source',
    'border-image-slice',
    'border-image-width',
    'border-image-outset',
    'border-image-repeat',
  ],
  'mask': [
    'mask-image',
    'mask-mode',
    'mask-position',
    'mask-size',
    'mask-repeat',
    'mask-origin',
    'mask-clip',
    'mask-composite',
  ],
  'inline-size': [
    'width',
    'height',
  ],
  'block-size': [
    'width',
    'height',
  ],
  'max-inline-size': [
    'max-width',
    'max-height',
  ],
  'max-block-size': [
    'max-width',
    'max-height',
  ],
  'inset': [
    'inset-block',
    'inset-block-start',
    'inset-block-end',
    'inset-inline',
    'inset-inline-start',
    'inset-inline-end',
    'top',
    'right',
    'bottom',
    'left',
  ],
  'inset-block': [
    'inset-block-start',
    'inset-block-end',
    'top',
    'right',
    'bottom',
    'left',
  ],
  'inset-inline': [
    'inset-inline-start',
    'inset-inline-end',
    'top',
    'right',
    'bottom',
    'left',
  ],
  'outline': [
    'outline-color',
    'outline-style',
    'outline-width',
  ],
  'overflow': [
    'overflow-x',
    'overflow-y',
  ],
  'place-content': [
    'align-content',
    'justify-content',
  ],
  'place-items': [
    'align-items',
    'justify-items',
  ],
  'place-self': [
    'align-self',
    'justify-self',
  ],
  'text-decoration': [
    'text-decoration-color',
    'text-decoration-style',
    'text-decoration-line',
  ],
  'transition': [
    'transition-delay',
    'transition-duration',
    'transition-property',
    'transition-timing-function',
  ],
  'text-emphasis': [
    'text-emphasis-style',
    'text-emphasis-color',
  ],
};
