# ODenta Final Seed Documentation

## Overview
The `finalSeed.js` file provides comprehensive database seeding for the ODenta university dental management system. This seed file populates the database with realistic data for all user roles and their associated records.

## What Gets Seeded

### 👥 User Accounts
- **3 Students** with different specializations
- **2 Supervisors** (faculty members)
- **2 Admins** (university administrators)
- **2 Assistants** (clinical assistants)
- **1 Superadmin** (system administrator)

### 🏥 Clinical Data
- **6 Patients** with complete medical histories
- **5 Appointments** (various types and statuses)
- **4 Procedure Requests** (pending, approved, rejected)
- **3 Lab Requests** (university and outside lab)
- **3 Reviews** (student work evaluations)
- **4 News Items** (university announcements)

## How to Run

### Prerequisites
1. Ensure MongoDB is running
2. Environment variables are properly configured
3. All dependencies are installed (`npm install`)

### Running the Seed

```bash
# Navigate to backend directory
cd dentlyzer-backend

# Run the comprehensive final seed
npm run final-seed

# Alternative: Run directly with node
node finalSeed.js
```

## Seeded Accounts

### 👨‍🎓 Students
| Email | Password | Name | Student ID |
|-------|----------|------|------------|
| <EMAIL> | STaiu-2025 | Ahmed Hassan | AIU001 |
| <EMAIL> | STaiu-2025 | Sara Mohamed | AIU002 |
| <EMAIL> | STaiu-2025 | Omar Ali | AIU003 |

### 👨‍⚕️ Supervisors
| Email | Password | Name |
|-------|----------|------|
| <EMAIL> | SVaiu-2025 | Dr. Mahmoud Farouk |
| <EMAIL> | SVaiu-2025 | Dr. Fatma Abdel Rahman |

### 👨‍💼 Admins
| Email | Password | Name |
|-------|----------|------|
| <EMAIL> | Aaiu-2025 | Dr. Khaled Ibrahim |
| <EMAIL> | Aaiu-2025 | Dr. Nadia Saleh |

### 🤝 Assistants
| Email | Password | Name |
|-------|----------|------|
| <EMAIL> | ASTaiu-2025 | Mona Youssef |
| <EMAIL> | ASTaiu-2025 | Heba Mostafa |

### 🔑 Superadmin
| Email | Password | Name |
|-------|----------|------|
| <EMAIL> | super-2025 | Super Admin |

## Sample Data Features

### Patients
- Diverse demographics (ages 25-42)
- Various medical conditions and medications
- Different chief complaints and occupations
- Complete contact information

### Appointments
- Different appointment types (Consultation, Cleaning, Treatment, Surgery)
- Various statuses (pending, completed)
- Realistic scheduling across multiple days
- Student-patient assignments

### Procedure Requests
- All 6 dental specialties represented:
  - Periodontics
  - Endodontics
  - Oral Surgery
  - Fixed Prosthodontics
  - Removable Prosthodontics
  - Operative
- Different request statuses (pending, approved, rejected)
- Assistant responses with notes

### Lab Requests
- University and outside lab types
- Various statuses (pending, approved, completed)
- Realistic lab work descriptions
- Assistant management workflow

### Reviews
- Student work evaluations
- Supervisor ratings and feedback
- Multi-step review processes
- Quality and interaction scores

### News
- University-specific announcements
- Global dental education news
- Bilingual content (English/Arabic)
- Equipment and technology updates

## Testing Scenarios

After running the seed, you can test:

1. **Student Login** - Access patient management, appointments, lab requests
2. **Supervisor Login** - Review student work, provide feedback
3. **Admin Login** - View analytics, manage university data
4. **Assistant Login** - Handle procedure/lab requests, patient management
5. **Cross-role Interactions** - Procedure approvals, review workflows

## Database Reset

The seed file automatically clears all existing data before seeding. To reset and re-seed:

```bash
npm run final-seed
```

## Notes

- All passwords are hashed using bcrypt
- University affiliation is set to 'AIU' (Alamein International University)
- Dates are set to realistic future dates for testing
- Medical information includes realistic conditions and medications
- All data follows the application's validation schemas

## Troubleshooting

If seeding fails:
1. Check MongoDB connection
2. Verify environment variables
3. Ensure all required models are properly imported
4. Check console output for specific error messages

The seed script provides detailed console output showing progress and any errors encountered.
