const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const connectDB = require('./config/db');
const Student = require('./models/Student');
const Supervisor = require('./models/Supervisor');
const Admin = require('./models/Admin');
const Superadmin = require('./models/Superadmin');
const Assistant = require('./models/Assistant');
const Dentist = require('./models/Dentist');
const Patient = require('./models/Patient');
const Appointment = require('./models/Appointment');
const TeethChart = require('./models/TeethChart');
const Review = require('./models/Review');

// Connect to MongoDB
connectDB();

const seedData = async () => {
  try {
    // Clear existing data
    await Student.deleteMany({});
    await Supervisor.deleteMany({});
    await Admin.deleteMany({});
    await Superadmin.deleteMany({});
    await Assistant.deleteMany({});
    await Dentist.deleteMany({});
    await Patient.deleteMany({});
    await Appointment.deleteMany({});
    await TeethChart.deleteMany({});
    await Review.deleteMany({});
    console.log('Database cleared');

    // Seed Students
    const students = [
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('STaiu-2025', 10),
        name: 'Student AIU',
        studentId: 'AIU001',
        role: 'student',
        university: 'AIU',
        patients: [],
        reviews: [],
      }
    ];
    const savedStudents = await Student.insertMany(students);
    console.log('Students seeded');

    // Seed Supervisor
    const supervisor = new Supervisor({
      email: '<EMAIL>',
      password: 'SVaiu-2025',
      name: 'Supervisor AIU',
      role: 'supervisor',
      university: 'AIU',
      pendingReviews: [],
      completedReviews: [],
    });
    const savedSupervisor = await supervisor.save();
    console.log('Supervisor seeded');

    // Seed Admin
    const admin = new Admin({
      email: '<EMAIL>',
      password: 'Aaiu-2025',
      name: 'Admin AIU',
      role: 'admin',
      university: 'AIU',
    });
    const savedAdmin = await admin.save();
    console.log('Admin seeded');

    // Seed Superadmin
    const superadmin = new Superadmin({
      email: '<EMAIL>',
      password: 'super-2025',
      name: 'Super Admin',
      role: 'superadmin',
    });
    const savedSuperadmin = await superadmin.save();
    console.log('Superadmin seeded');

    // Seed Assistant
    const assistant = new Assistant({
      email: '<EMAIL>',
      password: await bcrypt.hash('ASTaiu-2025', 10),
      name: 'Assistant AIU',
      role: 'assistant',
      university: 'AIU',
      affiliation: {
        type: 'university',
        id: 'AIU'
      }
    });
    const savedAssistant = await assistant.save();
    console.log('Assistant seeded');

    console.log('Database seeded successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

seedData();