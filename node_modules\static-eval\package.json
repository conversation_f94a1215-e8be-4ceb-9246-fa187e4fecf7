{"name": "static-eval", "version": "2.0.2", "description": "evaluate statically-analyzable expressions", "main": "index.js", "dependencies": {"escodegen": "^1.8.1"}, "devDependencies": {"esprima": "^2.7.3", "tape": "^4.6.0"}, "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "ff/latest", "chrome/latest", "opera/latest", "safari/latest"]}, "repository": {"type": "git", "url": "git://github.com/substack/static-eval.git"}, "homepage": "https://github.com/substack/static-eval", "keywords": ["static", "eval", "expression", "esprima", "ast", "abstract", "syntax", "tree", "analysis"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT"}